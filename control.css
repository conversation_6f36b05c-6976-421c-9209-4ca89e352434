* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

h1 {
    color: #2c3e50;
    font-size: 24px;
    font-weight: 600;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
}

#recording-status {
    font-weight: 500;
    color: #666;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #95a5a6;
    transition: all 0.3s ease;
}

.status-dot.recording {
    background: #e74c3c;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.main-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

h2 {
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
}

.option-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.option-card {
    cursor: pointer;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    background: #fff;
    position: relative;
    overflow: hidden;
}

.option-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.option-card input[type="radio"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.option-card input[type="radio"]:checked + .card-content {
    color: #667eea;
}

.option-card input[type="radio"]:checked + .card-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.card-content {
    text-align: center;
    position: relative;
}

.card-icon {
    font-size: 32px;
    margin-bottom: 10px;
}

.card-title {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 5px;
}

.card-description {
    font-size: 14px;
    color: #666;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.checkbox-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
}

.checkbox-option:hover {
    background-color: #f8f9fa;
}

.checkbox-option input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    margin-right: 12px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-option input[type="checkbox"]:checked + .checkmark {
    background-color: #667eea;
    border-color: #667eea;
}

.checkbox-option input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.label-text {
    font-size: 14px;
    color: #333;
}

.controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

.btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    min-width: 140px;
    justify-content: center;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #e74c3c;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #c0392b;
    transform: translateY(-2px);
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #229954;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

.results-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

@media (max-width: 768px) {
    .results-content {
        grid-template-columns: 1fr;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    .option-group {
        grid-template-columns: 1fr;
    }
}

.mouse-data h3,
.recording-data h3 {
    color: #2c3e50;
    font-size: 16px;
    margin-bottom: 15px;
}

#mouseStats {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
}

#mouseStats p {
    margin-bottom: 8px;
    font-size: 14px;
}

#mouseVisualization {
    margin-top: 15px;
    text-align: center;
}

#mouseVisualization canvas,
#mouseVisualization img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
